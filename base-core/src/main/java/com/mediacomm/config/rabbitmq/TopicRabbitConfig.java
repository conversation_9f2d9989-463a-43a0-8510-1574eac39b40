package com.mediacomm.config.rabbitmq;

import com.mediacomm.system.variable.MessageType;
import com.mediacomm.system.variable.RoutingKey;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 创建rabbitmq路由转发器.
 *
 * @author: WuZeJie.
 */
@Configuration
@Slf4j
public class TopicRabbitConfig {

  private static final String DEAD_LETTER_EXCHANGE = "dle-exchange";
  public static final String TOPIC_EXCHANGE = "topic-exchange";

  @Bean("webQueue")
  public Queue webQueue() {
    return budilDelayQueue(MessageType.SERVICE_WEB);
  }

  @Bean("ptzQueue")
  public Queue ptzQueue() {
    return budilDelayQueue(MessageType.PTZ_SERVER);
  }

  @Bean("kaitoQueue")
  public Queue kaitoQueue() {
    return budilDelayQueue(MessageType.KAITO02_KVM);
  }

  @Bean("caesarKvmQueue")
  public Queue caesarKvmQueue() {
    return budilDelayQueue(MessageType.CAESAR_KVM);
  }

  @Bean("airconKvmQueue")
  public Queue airconKvmQueue() {
    return budilDelayQueue(MessageType.AIRCON_KVM);
  }

  @Bean("hikvisionServerQueue")
  public Queue hikvisionServerQueue() {
    return budilDelayQueue(MessageType.HIKVISION_SERVER);
  }

  @Bean("tsingliServerQueue")
  public Queue tsingliServerQueue() {
    return budilDelayQueue(MessageType.TSINGLI_SERVER);
  }

  @Bean("kvmSwitcherQueue")
  public Queue kvmSwitcherQueue() {
    return budilDelayQueue(MessageType.KVM_SWITCHER);
  }

  @Bean("monitorQueue")
  public Queue monitorQueue() {
    return budilDelayQueue(MessageType.MONITOR);
  }

  @Bean("topicExchange")
  public TopicExchange exchange() {
    return new TopicExchange(TOPIC_EXCHANGE);
  }

  /**
   * 将消息携带的路由键为topic.queue.PTZ_SERVER的转发的ptz服务的队列中.
   */
  @Bean
  Binding bindingExchangeFromPtz() {
    return BindingBuilder.bind(ptzQueue()).to(exchange()).with(RoutingKey.PTZ_SERVER_OPT);
  }

  @Bean
  Binding bindingExchangeFromKaito() {
    return BindingBuilder.bind(kaitoQueue()).to(exchange()).with(RoutingKey.KAITO_KVM_OPT);
  }
  /**
   * 将消息携带的路由键为kvm.caesar.#的转发的CaesarKvm服务的队列中.
   */
  @Bean
  Binding bindingExchangeFromCaesarKvm() {
    return BindingBuilder.bind(caesarKvmQueue()).to(exchange()).with(RoutingKey.CAESAR_KVM_OPT);
  }

  /**
   * 将消息携带的路由键为kvm.aircon.#的转发的airconKvm服务的队列中.
   */
  @Bean
  Binding bindingExchangeFromAirconKvm() {
    return BindingBuilder.bind(airconKvmQueue()).to(exchange()).with(RoutingKey.AIRCON_KVM_OPT);
  }

  /**
   * 将消息携带的路由键为server.hikvision.#的转发的HikvisionServer服务的队列中.
   */
  @Bean
  Binding bindingExchangeFromHikvisionServer() {
    return BindingBuilder.bind(hikvisionServerQueue()).to(exchange())
        .with(RoutingKey.HIKVISION_SERVER_OPT);
  }

  /**
   * 将消息携带的路由键为server.tsingli.#的转发的TsingliServer服务的队列中.
   */
  @Bean
  Binding bindingExchangeFromTsingliServer() {
    return BindingBuilder.bind(tsingliServerQueue()).to(exchange())
        .with(RoutingKey.TSINGLI_SERVER_OPT);
  }

  @Bean
  Binding bindingExchangeFromKvmSwitcher() {
    return BindingBuilder.bind(kvmSwitcherQueue()).to(exchange())
        .with(RoutingKey.KVM_SWITCHER_OPT);
  }

  @Bean
  Binding bindingExchangeFromMonitor() {
    return BindingBuilder.bind(monitorQueue()).to(exchange())
        .with(RoutingKey.MONITOR_SERVER_OPT);
  }

  /**
   * 将消息携带的路由键为top.queue.web的转发的web服务的队列中.
   */
  @Bean
  Binding bindingExchangeFromWeb() {
    return BindingBuilder.bind(webQueue()).to(exchange()).with(RoutingKey.WEB_SERVER_OPT);
  }

  /**
   * 创建延迟队列.
   *
   * @param queueName 队列名称.
   * @return Queue.
   */
  private Queue budilDelayQueue(String queueName) {
    return QueueBuilder.durable(queueName)
        //如果消息过时，则会被投递到当前对应的exchange
        .withArgument("x-dead-letter-exchange", DEAD_LETTER_EXCHANGE)
        .withArgument("x-dead-letter-routing-key", RoutingKey.DEAD_LETTER_ROUTING)
        .withArgument("x-max-length", 3)
        .withArgument("x-message-ttl", 3000).build();
  }

  //定义死信队列
  @Bean
  public Queue dlqQueue() {
    return QueueBuilder.durable(RoutingKey.DEAD_LETTER_ROUTING).build();
  }

  //定义死信交换机
  @Bean
  public FanoutExchange dleExchange() {
    return new FanoutExchange(DEAD_LETTER_EXCHANGE);
  }

  //绑定死信队列与交换机
  @Bean
  public Binding dlxBinding() {
    return BindingBuilder.bind(dlqQueue()).to(dleExchange());
  }

  @RabbitListener(queues = RoutingKey.DEAD_LETTER_ROUTING)
  public void processFailedMessages(Message message) {
    log.warn("The message " + new String(message.getBody(), StandardCharsets.UTF_8)
        + " processing has not completed!");
  }
}

